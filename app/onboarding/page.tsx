"use client";
import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Spin } from 'antd';
import { CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { createWorkspace, checkWorkspaceIdAvailability } from '@/app/actions/workspace';
import Header from '@/app/components/Header';

const { Title, Text } = Typography;

interface FormValues {
  workspaceName: string;
  workspaceId: string;
}

const OnboardingPage = () => {
  const t = useTranslations('Onboarding');
  const router = useRouter();
  const { data: session, status } = useSession();
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingId, setIsCheckingId] = useState(false);
  const [idAvailable, setIdAvailable] = useState<boolean | null>(null);
  const [workspaceId, setWorkspaceId] = useState('');

  // 检查用户是否已登录
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  // 检查 workspace ID 可用性
  const checkIdAvailability = async (id: string) => {
    if (!id || id.length < 3) {
      setIdAvailable(null);
      return;
    }

    setIsCheckingId(true);
    try {
      const result = await checkWorkspaceIdAvailability(id);
      setIdAvailable(result.available);
    } catch (error) {
      setIdAvailable(false);
    } finally {
      setIsCheckingId(false);
    }
  };

  // 处理 workspace ID 输入变化
  const handleWorkspaceIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '');
    setWorkspaceId(value);
    form.setFieldsValue({ workspaceId: value });

    // 防抖检查 ID 可用性
    const timeoutId = setTimeout(() => {
      checkIdAvailability(value);
    }, 500);

    return () => clearTimeout(timeoutId);
  };

  // 处理表单提交
  const onFinish = async (values: FormValues) => {
    if (!idAvailable) {
      message.error(t('idUnavailableError'));
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createWorkspace({
        id: values.workspaceId,
        name: values.workspaceName,
      });

      if (result.status === 'success') {
        message.success(t('createSuccess'));
        // 跳转到新创建的 workspace 聊天页面
        router.push(`/${result.data?.id}/chat`);
      } else {
        message.error(result.message || t('createFailed'));
      }
    } catch (error) {
      message.error(t('createError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 生成建议的 workspace ID
  const generateSuggestedId = () => {
    const workspaceName = form.getFieldValue('workspaceName');
    if (workspaceName) {
      const suggested = workspaceName
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '')
        .slice(0, 10);
      setWorkspaceId(suggested);
      form.setFieldsValue({ workspaceId: suggested });
      checkIdAvailability(suggested);
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      {/* Main Content */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Title level={2}>{t('welcome')}</Title>
          <Text type="secondary">
            {t('subtitle')}
          </Text>
        </div>

        <Card className="shadow-lg">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item
              label={t('workspaceName')}
              name="workspaceName"
              rules={[
                { required: true, message: t('workspaceNameRequired') },
                { min: 2, message: t('workspaceNameMinLength') },
                { max: 50, message: t('workspaceNameMaxLength') }
              ]}
            >
              <Input
                placeholder={t('workspaceNamePlaceholder')}
                size="large"
                onBlur={generateSuggestedId}
              />
            </Form.Item>

            <Form.Item
              label={t('workspaceId')}
              name="workspaceId"
              rules={[
                { required: true, message: t('workspaceIdRequired') },
                { min: 5, message: t('workspaceIdMinLength') },
                { max: 20, message: t('workspaceIdMaxLength') },
                { pattern: /^[a-z0-9-]+$/, message: t('workspaceIdPattern') }
              ]}
              help={
                <div className="mt-2 mb-4">
                  <Text type="secondary" className="text-xs">
                    {t('workspaceIdHelp')}{workspaceId}
                  </Text>
                </div>
              }
            >
              <Input
                placeholder={t('workspaceIdPlaceholder')}
                size="large"
                value={workspaceId}
                onChange={handleWorkspaceIdChange}
                suffix={
                  isCheckingId ? (
                    <LoadingOutlined />
                  ) : idAvailable === true ? (
                    <CheckCircleOutlined style={{ color: '#52c41a' }} />
                  ) : idAvailable === false ? (
                    <Text type="danger" className="text-xs">{t('workspaceIdUnavailable')}</Text>
                  ) : null
                }
              />
            </Form.Item>

            <Form.Item className="mb-0">
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                block
                loading={isSubmitting}
                disabled={!idAvailable}
              >
                {t('createWorkspace')}
              </Button>
            </Form.Item>
          </Form>
        </Card>

          <div className="text-center">
            <Text type="secondary" className="text-sm">
              {t('teamNotice')}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingPage;